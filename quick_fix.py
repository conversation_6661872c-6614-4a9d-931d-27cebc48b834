#!/usr/bin/env python3
"""
量化交易系统快速修复脚本
一键修复潜在信号通知、择时信号通知和结算时间异常问题
"""

import asyncio
import os
import sys
import subprocess
from datetime import datetime


def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🚀 {title}")
    print("="*60)


def print_step(step_num, title):
    """打印步骤"""
    print(f"\n📋 步骤 {step_num}: {title}")
    print("-" * 50)


async def main():
    """主修复流程"""
    print_header("量化交易系统快速修复工具")
    
    print("📝 本工具将帮助您修复以下问题:")
    print("   1. 潜在信号通知问题 (10:45后停止发送)")
    print("   2. 择时信号通知缺失")
    print("   3. 信号结算时间异常 (不足10分钟)")
    
    # 确认用户要继续
    response = input("\n❓ 是否继续修复? (y/n): ").strip().lower()
    if response != 'y':
        print("❌ 用户取消修复")
        return
    
    # 步骤1: 检查Python环境
    print_step(1, "检查Python环境")
    try:
        python_version = sys.version
        print(f"✅ Python版本: {python_version}")
        
        # 检查所需模块
        required_modules = ['asyncio', 'sqlite3', 'json', 'datetime']
        for module in required_modules:
            try:
                __import__(module)
                print(f"✅ 模块 {module}: 已安装")
            except ImportError:
                print(f"❌ 模块 {module}: 未安装")
                
    except Exception as e:
        print(f"❌ 检查Python环境失败: {e}")
        return
    
    # 步骤2: 运行自动修复脚本
    print_step(2, "运行自动修复脚本")
    try:
        fix_script = "tests/fix_trading_system.py"
        if os.path.exists(fix_script):
            print(f"🔧 正在运行修复脚本: {fix_script}")
            result = subprocess.run([sys.executable, fix_script], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 自动修复脚本运行成功")
                print("📄 修复输出:")
                print(result.stdout)
            else:
                print("❌ 自动修复脚本运行失败")
                print("🐛 错误信息:")
                print(result.stderr)
                
        else:
            print(f"❌ 修复脚本不存在: {fix_script}")
            
    except Exception as e:
        print(f"❌ 运行修复脚本失败: {e}")
    
    # 步骤3: 运行调试脚本验证
    print_step(3, "运行调试脚本验证修复效果")
    try:
        debug_script = "tests/debug_trading_system.py"
        if os.path.exists(debug_script):
            print(f"🔍 正在运行调试脚本: {debug_script}")
            result = subprocess.run([sys.executable, debug_script], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 调试脚本运行成功")
                print("📊 调试输出:")
                print(result.stdout)
            else:
                print("❌ 调试脚本运行失败")
                print("🐛 错误信息:")
                print(result.stderr)
                
        else:
            print(f"❌ 调试脚本不存在: {debug_script}")
            
    except Exception as e:
        print(f"❌ 运行调试脚本失败: {e}")
    
    # 步骤4: 提供后续建议
    print_step(4, "后续建议")
    print("🎯 修复完成后，建议进行以下操作:")
    print("   1. 重新启动交易系统: python main.py")
    print("   2. 监控系统运行状态")
    print("   3. 检查钉钉通知是否正常")
    print("   4. 验证信号生成和结算功能")
    
    print("\n📚 详细信息请查看:")
    print("   - 修复指南: docs/trading_system_debug_guide.md")
    print("   - 调试脚本: tests/debug_trading_system.py")
    print("   - 修复脚本: tests/fix_trading_system.py")
    
    # 询问是否立即重启系统
    print("\n" + "="*60)
    restart_response = input("❓ 是否立即重启交易系统? (y/n): ").strip().lower()
    if restart_response == 'y':
        print("🚀 正在重启交易系统...")
        try:
            # 检查是否有旧的进程需要停止
            print("🔍 检查现有进程...")
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            if 'main.py' in result.stdout:
                print("⚠️ 检测到现有进程，请手动停止后重启")
                print("   使用命令: ps aux | grep main.py")
                print("   然后: kill -9 <process_id>")
            else:
                print("✅ 没有检测到现有进程")
                
                # 启动新进程
                print("🚀 启动新的交易系统进程...")
                subprocess.Popen([sys.executable, 'main.py'])
                print("✅ 交易系统已在后台启动")
                
        except Exception as e:
            print(f"❌ 重启交易系统失败: {e}")
            print("🔧 请手动运行: python main.py")
    
    print_header("修复完成")
    print("🎉 量化交易系统修复流程已完成！")
    print("📞 如有问题，请查看修复指南或联系技术支持")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断修复流程")
    except Exception as e:
        print(f"\n\n❌ 修复流程异常: {e}")
        print("🔧 请手动运行修复脚本或联系技术支持")